# STA Keeper Backend 重构优化指南

## 项目概述

STA Keeper Backend 是一个基于 FastAPI 的后端服务，主要用于钢铁制造过程中的数据管理、比对和监控。项目使用 SQLAlchemy 进行数据库操作，支持异步处理，并集成了多个数据源（MySQL、Oracle）。

## 重构优化目标

- 减少代码冗余
- 提高代码可读性和可维护性
- 优化性能
- 增强错误处理和日志记录
- 改进项目结构

## 重构优化建议

### 1. 配置管理优化

#### 当前问题
- 配置分散在多个文件中
- 硬编码的数据库连接信息和密码
- 环境变量处理不统一

#### 优化建议
1. **集中配置管理**
```python
# app/config/settings.py
from pydantic_settings import BaseSettings
from typing import Dict, Any, Optional
import os
from functools import lru_cache

class Settings(BaseSettings):
    """应用基础配置设置"""
    APP_NAME: str = "STA Keeper Backend"
    SECRET_KEY: str = "X7zQ2aP8vY3kL1wN9cB4rT6mJ0oG5sHdKfUxIeWqVlOyA_"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = ""
    DB_NAME: str = "sta_keeper"
    
    # Oracle数据库配置
    ERP_ORACLE_CONFIG: Dict[str, Any] = {
        "HOST": "************",
        "PORT": 1521,
        "SERVICE": "jgdb",
        "USER": "ERP_FB01",
        "PASSWORD": "erp_fb01"
    }
    
    MES_ORACLE_CONFIG: Dict[str, Any] = {
        "HOST": "************",
        "PORT": 1521,
        "SERVICE": "jgdb",
        "USER": "imes",
        "PASSWORD": "sa"
    }
    
    MES_MSG_ORACLE_CONFIG: Dict[str, Any] = {
        "HOST": "************",
        "PORT": 1521,
        "SERVICE": "jgdb",
        "USER": "INTERFACE_ERP_2",
        "PASSWORD": "INTERFACE_ERP_2"
    }
    
    # 日志配置
    LOG_TO_FILE: bool = False
    LOG_FILE: str = "app.log"
    LOG_LEVEL: str = "INFO"
    
    # 环境配置
    DEBUG: bool = False
    PRO: bool = False
    
    # ERP Web系统配置
    ERP_WEB_USERNAME: str = "J039760"
    ERP_WEB_PASSWORD: str = "2012qwer"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

class DevSettings(Settings):
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    
    # 开发环境特定配置
    # ...

class ProSettings(Settings):
    PRO: bool = True
    LOG_TO_FILE: bool = True
    
    # 生产环境特定配置
    # ...

@lru_cache()
def get_settings():
    """获取配置单例"""
    env = os.getenv("APP_ENV", "dev").lower()
    if env == "pro":
        return ProSettings()
    else:
        return DevSettings()
```

2. **使用环境变量和.env文件**
   - 创建.env.example文件作为模板
   - 敏感信息通过环境变量注入
   - 使用pydantic的env_file功能自动加载环境变量

### 2. 数据库连接优化

#### 当前问题
- 数据库连接管理分散
- Oracle连接处理不够优化
- 连接池配置不够灵活

#### 优化建议
1. **统一数据库连接管理**
```python
# app/database/connection_manager.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from urllib.parse import quote_plus
import cx_Oracle
import asyncio
from functools import wraps
from app.config import get_settings
from app.utils.logger import logger
from contextlib import asynccontextmanager

settings = get_settings()

# MySQL连接
def get_mysql_url():
    encoded_password = quote_plus(settings.DB_PASSWORD)
    return f"mysql+aiomysql://{settings.DB_USER}:{encoded_password}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}?charset=utf8mb4"

# 创建异步引擎
engine = create_async_engine(
    get_mysql_url(),
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=settings.DEBUG
)

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 数据库会话依赖
async def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        await db.close()

# Oracle连接管理器
class OracleManager:
    def __init__(self):
        # 初始化连接池
        self._init_oracle_client()
        self._connection_pools = {}
    
    def _init_oracle_client(self):
        """初始化Oracle客户端"""
        try:
            # 可以设置Oracle客户端环境
            pass
        except Exception as e:
            logger.error(f"初始化Oracle客户端失败: {e}")
    
    def _get_connection_pool(self, config):
        """获取或创建连接池"""
        key = f"{config['USER']}@{config['HOST']}:{config['PORT']}/{config['SERVICE']}"
        
        if key not in self._connection_pools:
            try:
                dsn = cx_Oracle.makedsn(
                    config['HOST'], 
                    config['PORT'], 
                    service_name=config['SERVICE']
                )
                
                # 创建连接池
                pool = cx_Oracle.SessionPool(
                    user=config['USER'],
                    password=config['PASSWORD'],
                    dsn=dsn,
                    min=2,
                    max=10,
                    increment=1,
                    encoding="UTF-8"
                )
                
                self._connection_pools[key] = pool
                logger.info(f"创建Oracle连接池成功: {key}")
            except Exception as e:
                logger.error(f"创建Oracle连接池失败: {key}, 错误: {e}")
                raise
        
        return self._connection_pools[key]
    
    def get_connection(self, config):
        """获取数据库连接"""
        pool = self._get_connection_pool(config)
        return pool.acquire()
    
    def close_connection(self, connection):
        """关闭数据库连接"""
        if connection:
            try:
                connection.close()
            except Exception as e:
                logger.error(f"关闭Oracle连接失败: {e}")
    
    def get_erp_connection(self):
        """获取ERP数据库连接"""
        return self.get_connection(settings.ERP_ORACLE_CONFIG)
    
    def get_mes_connection(self):
        """获取MES数据库连接"""
        return self.get_connection(settings.MES_ORACLE_CONFIG)
    
    def get_mes_msg_connection(self):
        """获取MES消息数据库连接"""
        return self.get_connection(settings.MES_MSG_ORACLE_CONFIG)
    
    def execute_query(self, connection, sql, params=None):
        """执行查询"""
        cursor = connection.cursor()
        try:
            cursor.execute(sql, params or {})
            columns = [col[0] for col in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        finally:
            cursor.close()
    
    def execute_many(self, connection, sql, params_list):
        """批量执行"""
        cursor = connection.cursor()
        try:
            cursor.executemany(sql, params_list)
            connection.commit()
            return cursor.rowcount
        finally:
            cursor.close()
    
    def execute_procedure(self, connection, proc_name, params=None):
        """执行存储过程"""
        cursor = connection.cursor()
        try:
            return cursor.callproc(proc_name, params or [])
        finally:
            cursor.close()

# 异步Oracle执行装饰器
def async_oracle_query(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: func(*args, **kwargs))
    return wrapper

# 创建Oracle管理器实例
oracle_manager = OracleManager()
```

2. **使用上下文管理器简化连接管理**
```python
@asynccontextmanager
async def oracle_transaction(get_connection_func):
    """Oracle事务上下文管理器"""
    connection = None
    try:
        connection = get_connection_func()
        yield connection
        connection.commit()
    except Exception as e:
        if connection:
            connection.rollback()
        raise
    finally:
        if connection:
            oracle_manager.close_connection(connection)
```

### 3. 异常处理优化

#### 当前问题
- 异常处理不统一
- 缺少详细的异常类型
- 错误信息不够明确

#### 优化建议
1. **扩展异常类型**
```python
# app/exceptions.py
from fastapi import HTTPException, status
from typing import Optional, Dict, Any

class APIException(HTTPException):
    """基础API异常类"""
    def __init__(
        self,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        detail: str = None,
        code: int = None,
        data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.code = code if code is not None else status_code
        self.data = data or {}

# 用户相关异常
class UserException(APIException):
    """用户相关异常基类"""
    pass

class UserAlreadyExists(UserException):
    """用户已存在异常"""
    def __init__(self, username: str, detail: str = None):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail or f"用户 '{username}' 已存在",
            code=1001,
            data={"username": username}
        )

class UserNotFound(UserException):
    """用户不存在异常"""
    def __init__(self, username: str, detail: str = None):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail or f"用户 '{username}' 不存在",
            code=1002,
            data={"username": username}
        )

class InvalidCredentials(UserException):
    """无效凭证异常"""
    def __init__(self, detail: str = "用户名或密码错误"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            code=1003
        )

# 数据库相关异常
class DatabaseException(APIException):
    """数据库相关异常基类"""
    pass

class DatabaseConnectionError(DatabaseException):
    """数据库连接错误"""
    def __init__(self, db_name: str, detail: str = None):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail or f"无法连接到数据库 '{db_name}'",
            code=2001,
            data={"db_name": db_name}
        )

class QueryExecutionError(DatabaseException):
    """查询执行错误"""
    def __init__(self, detail: str = "查询执行失败"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            code=2002
        )

# 业务逻辑相关异常
class BusinessException(APIException):
    """业务逻辑相关异常基类"""
    pass

class TaskExecutionError(BusinessException):
    """任务执行错误"""
    def __init__(self, task_id: int, detail: str = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail or f"任务 {task_id} 执行失败",
            code=3001,
            data={"task_id": task_id}
        )

class InvalidTaskStatus(BusinessException):
    """无效的任务状态"""
    def __init__(self, task_id: int, current_status: str, expected_status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务 {task_id} 当前状态为 '{current_status}'，无法执行此操作，期望状态为 '{expected_status}'",
            code=3002,
            data={
                "task_id": task_id,
                "current_status": current_status,
                "expected_status": expected_status
            }
        )
```

2. **全局异常处理器**
```python
# app/utils/exception_handlers.py
from fastapi import Request, FastAPI
from fastapi.responses import JSONResponse
from app.exceptions import APIException
from app.utils.logger import logger
from app.utils.response import error

def register_exception_handlers(app: FastAPI):
    """注册全局异常处理器"""
    
    @app.exception_handler(APIException)
    async def api_exception_handler(request: Request, exc: APIException):
        """处理API异常"""
        logger.warning(f"API异常: {exc.detail}, 代码: {exc.code}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "code": exc.code,
                "msg": exc.detail,
                "data": exc.data if hasattr(exc, "data") else None
            }
        )
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """处理所有未捕获的异常"""
        logger.error(f"未捕获的异常: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content=error(f"服务器内部错误: {str(exc)}")
        )
```

### 4. 日志系统优化

#### 当前问题
- 日志配置不够灵活
- 缺少结构化日志
- 日志级别控制不够精细

#### 优化建议
1. **增强日志系统**
```python
# app/utils/logger.py
import logging
import json
import sys
import os
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from app.config import get_settings

settings = get_settings()

class StructuredLogFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        """格式化日志记录"""
        log_data = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
        
        # 添加额外字段
        if hasattr(record, "extra") and record.extra:
            log_data.update(record.extra)
        
        return json.dumps(log_data)

def setup_logging():
    """设置日志系统"""
    # 创建日志目录
    if settings.LOG_TO_FILE:
        os.makedirs("logs", exist_ok=True)
    
    # 获取日志级别
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # 创建根日志器
    logger = logging.getLogger(settings.APP_NAME)
    logger.setLevel(log_level)
    logger.handlers = []  # 清除现有处理器
    
    # 创建格式化器
    if settings.DEBUG:
        # 开发环境使用可读格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        # 生产环境使用JSON格式
        formatter = StructuredLogFormatter()
    
    if settings.LOG_TO_FILE:
        # 按大小轮转的文件处理器
        file_handler = RotatingFileHandler(
            f"logs/{settings.LOG_FILE}",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 按时间轮转的错误日志处理器
        error_handler = TimedRotatingFileHandler(
            f"logs/error.log",
            when="midnight",
            interval=1,
            backupCount=30
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)
    else:
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING if settings.DEBUG else logging.ERROR)
    
    return logger

# 创建日志器实例
logger = setup_logging()

# 扩展日志方法
def log_with_context(level, message, **kwargs):
    """带上下文的日志记录"""
    extra = {"extra": kwargs}
    logger.log(level, message, extra=extra)

def debug(message, **kwargs):
    """调试日志"""
    log_with_context(logging.DEBUG, message, **kwargs)

def info(message, **kwargs):
    """信息日志"""
    log_with_context(logging.INFO, message, **kwargs)

def warning(message, **kwargs):
    """警告日志"""
    log_with_context(logging.WARNING, message, **kwargs)

def error(message, **kwargs):
    """错误日志"""
    log_with_context(logging.ERROR, message, **kwargs)

def critical(message, **kwargs):
    """严重错误日志"""
    log_with_context(logging.CRITICAL, message, **kwargs)
```

2. **使用上下文日志**
```python
# 使用示例
from app.utils.logger import info, error

# 普通日志
info("用户登录成功")

# 带上下文的日志
info("用户登录成功", user_id=123, ip="***********", action="login")

# 错误日志
try:
    # 业务逻辑
    pass
except Exception as e:
    error("操作失败", operation="data_sync", error=str(e))
    raise
```

### 5. 服务层优化

#### 当前问题
- 服务层代码冗余
- 缺少基础服务类
- 异步处理不够优化

#### 优化建议
1. **创建基础服务类**
```python
# app/services/base.py
from sqlalchemy.ext.asyncio import AsyncSession
from typing import TypeVar, Generic, Type, Optional, List, Dict, Any
from sqlalchemy import select, update, delete
from app.database.session import Base
from app.utils.logger import logger

T = TypeVar('T', bound=Base)

class BaseService(Generic[T]):
    """基础服务类"""
    
    def __init__(self, db: AsyncSession, model_class: Type[T]):
        self.db = db
        self.model_class = model_class
    
    async def get_by_id(self, id: Any) -> Optional[T]:
        """根据ID获取记录"""
        stmt = select(self.model_class).where(self.model_class.id == id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_all(self) -> List[T]:
        """获取所有记录"""
        stmt = select(self.model_class)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def create(self, **kwargs) -> T:
        """创建记录"""
        obj = self.model_class(**kwargs)
        self.db.add(obj)
        await self.db.commit()
        await self.db.refresh(obj)
        return obj
    
    async def update(self, id: Any, **kwargs) -> Optional[T]:
        """更新记录"""
        stmt = update(self.model_class).where(
            self.model_class.id == id
        ).values(**kwargs).returning(self.model_class)
        result = await self.db.execute(stmt)
        await self.db.commit()
        return result.scalar_one_or_none()
    
    async def delete(self, id: Any) -> bool:
        """删除记录"""
        stmt = delete(self.model_class).where(self.model_class.id == id)
        result = await self.db.execute(stmt)
        await self.db.commit()
        return result.rowcount > 0
    
    async def exists(self, **kwargs) -> bool:
        """检查记录是否存在"""
        conditions = [getattr(self.model_class, k) == v for k, v in kwargs.items()]
        stmt = select(self.model_class).where(*conditions)
        result = await self.db.execute(stmt)
        return result.first() is not None
    
    async def count(self, **kwargs) -> int:
        """计数"""
        from sqlalchemy import func
        conditions = [getattr(self.model_class, k) == v for k, v in kwargs.items()]
        stmt = select(func.count()).select_from(self.model_class).where(*conditions)
        result = await self.db.execute(stmt)
        return result.scalar_one()
    
    async def find_by(self, **kwargs) -> List[T]:
        """根据条件查找"""
        conditions = [getattr(self.model_class, k) == v for k, v in kwargs.items()]
        stmt = select(self.model_class).where(*conditions)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def find_one_by(self, **kwargs) -> Optional[T]:
        """根据条件查找单个记录"""
        conditions = [getattr(self.model_class, k) == v for k, v in kwargs.items()]
        stmt = select(self.model_class).where(*conditions)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
```

2. **优化数据比对服务**
```python
# app/services/steel_making/erp/data_comparison.py
from app.services.base import BaseService
from app.models.steel_making.erp.data_comparison import DataComparisonTask
from app.models.common import TaskStatus, Factory
from app.database.connection_manager import oracle_manager, async_oracle_query
from app.utils.logger import logger
from typing import Dict, List, Optional, Callable
from datetime import datetime
import asyncio

class DataComparisonService(BaseService[DataComparisonTask]):
    """数据对比服务"""
    
    def __init__(self, db):
        super().__init__(db, DataComparisonTask)
    
    async def create_task(
        self,
        start_date: str,
        end_date: str,
        factory: Factory,
        heat_no: Optional[str] = None,
        material_code: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> DataComparisonTask:
        """创建数据对比任务"""
        task_name = f"每日数据对比_{factory.value}_{start_date}_{end_date}"
        if heat_no:
            task_name += f"_{heat_no}"
        if material_code:
            task_name += f"_{material_code}"

        return await self.create(
            task_name=task_name,
            start_date=start_date,
            end_date=end_date,
            factory=factory,
            heat_no=heat_no,
            material_code=material_code,
            created_by=user_id
        )
    
    async def start_task(self, task_id: int, progress_callback: Optional[Callable] = None):
        """启动数据对比任务"""
        try:
            # 更新任务状态为运行中
            await self._update_task_status(task_id, TaskStatus.RUNNING, started_at=datetime.now())
            
            # 获取任务信息
            task = await self.get_by_id(task_id)
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")
            
            # 执行数据对比
            await self._execute_comparison(task, progress_callback)
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id, 
                TaskStatus.COMPLETED, 
                progress=100,
                completed_at=datetime.now()
            )
            
            logger.info(f"数据对比任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"数据对比任务 {task_id} 执行失败: {e}")
            await self._update_task_status(
                task_id, 
                TaskStatus.FAILED, 
                error_message=str(e),
                completed_at=datetime.now()
            )
            raise
    
    # 其他方法保持不变...
```

### 6. 路由优化

#### 当前问题
- 路由注册分散
- 缺少路由分组
- 权限控制不够灵活

#### 优化建议
1. **集中路由注册**
```python
# app/routers/__init__.py
from fastapi import APIRouter
from app.routers.system import auth, user, role, user_role, operation_logs
from app.routers.steel_making.mes import manual_warehouse, mes_maintenance, mes_message_check
from app.routers.steel_making.erp import (
    auto_schedule, data_comparison, heat_no_switch, initial_casting, 
    ix_receiva, ix_batch_check, maintenance_history, message_check, 
    steel_cost_data_verification
)
from app.routers.steel_making.common import task_result_details

# 创建主路由
api_router = APIRouter(prefix="/api")

# 系统管理路由
system_router = APIRouter(prefix="/system")
system_router.include_router(auth.router, prefix="", tags=["auth"])
system_router.include_router(user.router, tags=["user"])
system_router.include_router(role.router, tags=["role"])
system_router.include_router(user_role.router, tags=["user-role"])
system_router.include_router(operation_logs.router, tags=["operation-logs"])

# 钢铁制造 - ERP路由
erp_router = APIRouter(prefix="/erp")
erp_router.include_router(data_comparison.router, tags=["data-comparison"])
erp_router.include_router(message_check.router, tags=["message-check"])
erp_router.include_router(auto_schedule.router, tags=["auto-schedule"])
erp_router.include_router(initial_casting.router, tags=["initial-casting"])
erp_router.include_router(maintenance_history.router, tags=["maintenance-history"])
erp_router.include_router(ix_receiva.router, tags=["ix-receiva"])
erp_router.include_router(steel_cost_data_verification.router, tags=["steel-cost-data-verification"])
erp_router.include_router(heat_no_switch.router, tags=["heat-no-switch"])
erp_router.include_router(ix_batch_check.router, tags=["ix-batch-check"])

# 钢铁制造 - MES路由
mes_router = APIRouter(prefix="/mes")
mes_router.include_router(mes_message_check.router, tags=["mes-message-check"])
mes_router.include_router(mes_maintenance.router, tags=["mes-maintenance"])
mes_router.include_router(manual_warehouse.router, tags=["manual-warehouse"])

# 钢铁制造 - 通用路由
common_router = APIRouter(prefix="/common")
common_router.include_router(task_result_details.router, tags=["task-result-details"])

# 将所有子路由注册到主路由
api_router.include_router(system_router)
api_router.include_router(erp_router, prefix="/steel-making")
api_router.include_router(mes_router, prefix="/steel-making")
api_router.include_router(common_router, prefix="/steel-making")

# 导出路由
def get_app_router():
    return api_router
```

2. **权限控制优化**
```python
# app/utils/permissions.py
from fastapi import Depends, HTTPException, status
from typing import List, Callable, Optional
from functools import wraps
from app.services.system.auth import get_current_user
from app.models.system.user import User

def require_permissions(required_permissions: List[str]):
    """权限检查装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, current_user: User = Depends(get_current_user), **kwargs):
            # 检查用户是否有所需权限
            user_permissions = getattr(current_user, 'permissions', [])
            
            # 超级管理员权限检查
            if "*" in user_permissions:
                return await func(*args, current_user=current_user, **kwargs)
            
            # 普通权限检查
            has_permission = any(perm in user_permissions for perm in required_permissions)
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足，无法执行此操作"
                )
            
            return await func(*args, current_user=current_user, **kwargs)
        return wrapper
    return decorator

# 使用示例
@router.get("/users")
@require_permissions(["user:list"])
async def list_users(current_user: User = Depends(get_current_user)):
    # 实现列出用户的逻辑
    pass
```

### 7. 模型优化

#### 当前问题
- 模型定义分散
- 缺少基础模型类
- 字段类型和注释不统一

#### 优化建议
1. **创建基础模型类**
```python
# app/models/base.py
from datetime import datetime
from typing import Optional
from sqlalchemy import BigInteger, DateTime
from sqlalchemy.orm import Mapped, mapped_column
from app.database.session import Base

class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    def to_dict(self):
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result

class BaseTaskModel(BaseModel):
    """基础任务模型类"""
    __abstract__ = True
    
    task_name: Mapped[str]
    status: Mapped[str]
    progress: Mapped[int] = mapped_column(default=0, comment="进度(0-100)")
    error_message: Mapped[Optional[str]] = mapped_column(comment="错误信息")
    started_at: Mapped[Optional[datetime]] = mapped_column(comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(comment="完成时间")
    created_by: Mapped[Optional[int]] = mapped_column(comment="创建人ID")
```

2. **优化用户模型**
```python
# app/models/system/user.py
from datetime import datetime
from typing import List, Optional
from sqlalchemy import String, Integer, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import BaseModel
from app.utils.security import get_password_hash, verify_password

class User(BaseModel):
    """用户模型"""
    __tablename__ = "sys_user"
    __table_args__ = {"comment": "系统用户表"}

    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, comment="用户名")
    password: Mapped[str] = mapped_column(String(255), nullable=False, comment="密码")
    nickname: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="昵称")
    remark: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="备注")
    status: Mapped[int] = mapped_column(Integer, default=1, comment="状态(0:禁用,1:启用)")
    
    # 关系
    roles = relationship("UserRole", back_populates="user", lazy="selectin")
    
    # 非持久化属性
    permissions: List[str] = []
    
    def set_password(self, password: str):
        """设置密码(加密)"""
        self.password = get_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        return verify_password(password, self.password)
    
    def __repr__(self):
        return f"<User(id={self.id}, username={self.username})>"
```

### 8. 调度器优化

#### 当前问题
- 调度器代码冗余
- 任务执行逻辑重复
- 错误处理不够健壮

#### 优化建议
1. **重构调度器**
```python
# app/services/common/scheduler.py
from datetime import datetime, timedelta
from typing import Dict, Any, Callable, Optional, List
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.job import Job
from app.utils.logger import logger
import asyncio

class TaskDefinition:
    """任务定义"""
    def __init__(
        self,
        func: Callable,
        trigger,
        job_id: str,
        name: str,
        max_instances: int = 1,
        coalesce: bool = True,
        misfire_grace_time: int = 300,
        args: List = None,
        kwargs: Dict = None
    ):
        self.func = func
        self.trigger = trigger
        self.job_id = job_id
        self.name = name
        self.max_instances = max_instances
        self.coalesce = coalesce
        self.misfire_grace_time = misfire_grace_time
        self.args = args or []
        self.kwargs = kwargs or {}

class SchedulerService:
    """调度器服务"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.tasks = []
    
    def register_task(self, task_def: TaskDefinition):
        """注册任务"""
        self.tasks.append(task_def)
        if self.is_running:
            self._add_job(task_def)
    
    def _add_job(self, task_def: TaskDefinition) -> Optional[Job]:
        """添加任务到调度器"""
        try:
            return self.scheduler.add_job(
                func=task_def.func,
                trigger=task_def.trigger,
                id=task_def.job_id,
                name=task_def.name,
                max_instances=task_def.max_instances,
                coalesce=task_def.coalesce,
                misfire_grace_time=task_def.misfire_grace_time,
                args=task_def.args,
                kwargs=task_def.kwargs
            )
        except Exception as e:
            logger.error(f"添加任务失败: {task_def.name}, 错误: {e}")
            return None
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
        
        try:
            # 添加所有注册的任务
            for task_def in self.tasks:
                self._add_job(task_def)
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            logger.info("调度器已启动")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("调度器已停止")
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    def get_job_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running",
            "jobs": jobs
        }
    
    def pause_job(self, job_id: str) -> bool:
        """暂停任务"""
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"任务已暂停: {job_id}")
            return True
        except Exception as e:
            logger.error(f"暂停任务失败: {job_id}, 错误: {e}")
            return False
    
    def resume_job(self, job_id: str) -> bool:
        """恢复任务"""
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"任务已恢复: {job_id}")
            return True
        except Exception as e:
            logger.error(f"恢复任务失败: {job_id}, 错误: {e}")
            return False
    
    def modify_job(self, job_id: str, **changes) -> bool:
        """修改任务"""
        try:
            self.scheduler.modify_job(job_id, **changes)
            logger.info(f"任务已修改: {job_id}")
            return True
        except Exception as e:
            logger.error(f"修改任务失败: {job_id}, 错误: {e}")
            return False

# 创建调度器服务实例
scheduler_service = SchedulerService()

# 任务执行装饰器
def task_wrapper(func):
    """任务执行装饰器，用于统一处理任务执行过程"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        task_name = func.__name__
        logger.info(f"开始执行任务: {task_name}")
        start_time = datetime.now()
        
        try:
            result = await func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"任务执行完成: {task_name}, 耗时: {duration:.2f}秒")
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"任务执行失败: {task_name}, 耗时: {duration:.2f}秒, 错误: {e}", exc_info=True)
            # 可以在这里添加错误通知逻辑
            raise
    
    return wrapper
```

2. **使用示例**
```python
# 注册任务示例
from app.services.common.scheduler import scheduler_service, TaskDefinition, task_wrapper
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime

@task_wrapper
async def data_comparison_task():
    """数据对比任务"""
    # 任务实现...
    pass

# 注册任务
scheduler_service.register_task(
    TaskDefinition(
        func=data_comparison_task,
        trigger=IntervalTrigger(hours=2),
        job_id='data_comparison_job',
        name='物料消耗对比',
        max_instances=1
    )
)
```

### 9. 依赖注入优化

#### 当前问题
- 服务实例化分散
- 依赖注入不够清晰
- 代码重复

#### 优化建议
1. **创建依赖注入容器**
```python
# app/utils/dependencies.py
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Callable, Dict, Type, TypeVar, Generic, Any
from app.database.session import get_db
from app.services.base import BaseService
from app.models.system.user import User
from app.services.system.auth import get_current_user

T = TypeVar('T')

class ServiceFactory(Generic[T]):
    """服务工厂"""
    
    def __init__(self, service_class: Type[T]):
        self.service_class = service_class
    
    def __call__(self, db: AsyncSession = Depends(get_db)) -> T:
        return self.service_class(db)

# 用户服务依赖
from app.services.system.user_service import UserService
get_user_service = ServiceFactory(UserService)

# 角色服务依赖
from app.services.system.role_service import RoleService
get_role_service = ServiceFactory(RoleService)

# 数据对比服务依赖
from app.services.steel_making.erp.data_comparison import DataComparisonService
get_data_comparison_service = ServiceFactory(DataComparisonService)

# 电文检查服务依赖
from app.services.steel_making.erp.message_check import MessageCheckService
get_message_check_service = ServiceFactory(MessageCheckService)

# 获取当前用户和服务
async def get_current_user_and_service(
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """获取当前用户和用户服务"""
    return current_user, user_service
```

2. **使用依赖注入**
```python
# 路由中使用依赖注入
from fastapi import APIRouter, Depends
from app.utils.dependencies import get_user_service, get_current_user_and_service
from app.services.system.user_service import UserService
from app.models.system.user import User

router = APIRouter()

@router.get("/users")
async def list_users(
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_user)
):
    """列出所有用户"""
    users = await user_service.get_all_users()
    return {"users": users}

@router.get("/users/me/roles")
async def get_my_roles(
    current_user_and_service: tuple = Depends(get_current_user_and_service)
):
    """获取当前用户的角色"""
    current_user, user_service = current_user_and_service
    roles = await user_service.get_user_roles(current_user.id)
    return {"roles": roles}
```

### 10. 响应模型优化

#### 当前问题
- 响应格式不统一
- 缺少类型提示
- 数据转换不够灵活

#### 优化建议
1. **增强响应模型**
```python
# app/utils/response.py
from fastapi import status
from typing import TypeVar, Generic, Optional, Any, Dict, List
from pydantic import BaseModel, Field
from datetime import datetime

T = TypeVar('T')

class ResponseModel(BaseModel, Generic[T]):
    """API响应模型"""
    code: int = Field(..., description="状态码")
    msg: str = Field(..., description="消息")
    data: Optional[T] = Field(None, description="数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

class PaginationModel(BaseModel, Generic[T]):
    """分页响应模型"""
    items: List[T] = Field(..., description="数据项")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")

def success(data: Any = None, msg: str = "操作成功") -> Dict[str, Any]:
    """成功响应"""
    return ResponseModel(
        code=200,
        msg=msg,
        data=data
    ).dict()

def fail(msg: str = "操作失败", data: Any = None) -> Dict[str, Any]:
    """失败响应"""
    return ResponseModel(
        code=-1,
        msg=msg,
        data=data
    ).dict()

def error(msg: str = "服务器异常", data: Any = None) -> Dict[str, Any]:
    """错误响应"""
    return ResponseModel(
        code=500,
        msg=msg,
        data=data
    ).dict()

def unauthorized(msg: str = "未授权", data: Any = None) -> Dict[str, Any]:
    """未授权响应"""
    return ResponseModel(
        code=401,
        msg=msg,
        data=data
    ).dict()

def paginate(items: List[Any], total: int, page: int, size: int) -> Dict[str, Any]:
    """分页响应"""
    pages = (total + size - 1) // size if size > 0 else 0
    return success(
        PaginationModel(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages
        ).dict()
    )
```

2. **使用响应模型**
```python
from fastapi import APIRouter, Depends, Query
from app.utils.response import success, paginate
from app.utils.dependencies import get_user_service
from app.services.system.user_service import UserService
from typing import List
from app.schemas.user import UserOut

router = APIRouter()

@router.get("/users", response_model=ResponseModel[List[UserOut]])
async def list_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    user_service: UserService = Depends(get_user_service)
):
    """列出所有用户"""
    users, total = await user_service.get_users_paginated(page, size)
    return paginate(users, total, page, size)
```

## 实施计划

为了有序地实施上述重构优化建议，建议按照以下步骤进行：

1. **基础设施优化**
   - 配置管理优化
   - 数据库连接优化
   - 日志系统优化
   - 异常处理优化

2. **核心组件重构**
   - 基础模型类创建
   - 基础服务类创建
   - 响应模型优化
   - 依赖注入容器

3. **业务逻辑优化**
   - 服务层重构
   - 调度器优化
   - 权限控制优化

4. **接口层优化**
   - 路由组织优化
   - API文档完善
   - 请求验证增强

5. **测试与部署**
   - 单元测试编写
   - 集成测试编写
   - CI/CD流程优化

## 预期收益

通过实施上述重构优化建议，预期可以获得以下收益：

1. **代码量减少**：通过消除重复代码、引入基类和通用组件，预计可减少20-30%的代码量。

2. **可读性提升**：统一的代码风格、清晰的项目结构和完善的注释将大幅提高代码可读性。

3. **可维护性增强**：模块化设计、依赖注入和统一的错误处理使代码更易于维护和扩展。

4. **性能优化**：连接池优化、异步处理改进和缓存策略将提升系统性能。

5. **开发效率提高**：基础组件和工具类的引入将加速新功能的开发和问题的修复。

## 结论

STA Keeper Backend项目具有良好的基础架构，但通过上述重构优化建议，可以进一步提升代码质量、减少代码量并增强系统的可维护性和可扩展性。建议按照实施计划逐步进行优化，确保系统稳定运行的同时不断改进代码质量。
